{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogForm.vue", "mtime": 1754376687851}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgbGl0aWdhdGlvblN0YXR1cyBmcm9tICdAL2xheW91dC9jb21wb25lbnRzL0RpYWxvZy9saXRpZ2F0aW9uU3RhdHVzLnZ1ZScNCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAnQC91dGlscy9hdXRoJw0KaW1wb3J0IHsgc3VibWl0TGl0aWdhdGlvbkxvZyB9IGZyb20gJ0AvYXBpL2xpdGlnYXRpb24vbGl0aWdhdGlvbicNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnTGl0aWdhdGlvbkxvZ0Zvcm0nLA0KICBjb21wb25lbnRzOiB7DQogICAgbGl0aWdhdGlvblN0YXR1cywNCiAgfSwNCiAgcHJvcHM6IHsNCiAgICBhY3Rpb246IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcvY29tbW9uL29zc3VwbG9hZCcsDQogICAgfSwNCiAgICBkYXRhOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiB7fSwNCiAgICB9LA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB0aXRsZTogJ+aPkOS6pOazleivieaXpeW/lycsDQogICAgICB2aXNpYmxlOiBmYWxzZSwNCiAgICAgIGxvYW5SZW1pbmRlcjoge30sDQogICAgICBsaXRpZ2F0aW9uTG9nOiB7fSwNCiAgICAgIGluc3RhbGxtZW50Rm9ybTogew0KICAgICAgICBhcHBseUFtb3VudDogMCwNCiAgICAgICAgcGVyaW9kQ291bnQ6IDEsDQogICAgICAgIGJpbGxBbW91bnQ6ICcwLjAwJywNCiAgICAgICAgdGFpbEFtb3VudDogMCwNCiAgICAgICAgcmVwYXlEYXk6IDEsDQogICAgICAgIHRhaWxQYXlUaW1lOiBudWxsLA0KICAgICAgICBhY2NvdW50VHlwZTogJycsDQogICAgICAgIGluc3RhbGxtZW50U3RhdHVzOiAyIC8vIDIt5rOV6K+J5YiG5pyfDQogICAgICB9LA0KICAgICAgdXBsb2FkVXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgdGhpcy5hY3Rpb24sDQogICAgICBoZWFkZXJzOiB7DQogICAgICAgIEF1dGhvcml6YXRpb246ICdCZWFyZXIgJyArIGdldFRva2VuKCksDQogICAgICB9LA0KICAgICAgZGlhbG9nSW1hZ2VVcmw6ICcnLA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgIGRhdGE6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsKSB7DQogICAgICAgIGlmIChuZXdWYWwpIHsNCiAgICAgICAgICBjb25zb2xlLmxvZygnbmV3VmFsJywgbmV3VmFsKQ0KICAgICAgICAgIHRoaXMubG9hblJlbWluZGVyID0gew0KICAgICAgICAgICAgbG9hbklkOiBuZXdWYWwu5rWB56iL5bqP5Y+3LA0KICAgICAgICAgICAgY3VzdG9tZXJOYW1lOiBuZXdWYWwu6LS35qy+5Lq6LA0KICAgICAgICAgICAgY2hhbm5lbDogbmV3VmFsLuWHuuWNlea4oOmBkywNCiAgICAgICAgICAgIGJhbms6IG5ld1ZhbC7mlL7mrL7pk7booYwsDQogICAgICAgICAgICBpZGVudGl0eTogdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlc1swXSwNCiAgICAgICAgICAgIHJlcGF5bWVudFN0YXR1czogJycsDQogICAgICAgICAgICBmdW5kc1JlcGF5bWVudDogJycsDQogICAgICAgICAgICBmdW5kc0Ftb3VudDogJycsDQogICAgICAgICAgICBmdW5kc0ltYWdlOiBbXSwNCiAgICAgICAgICAgIGZ1bmRzQWNjb3VudFR5cGU6ICcnLA0KICAgICAgICAgICAgYWNjb3VudE51bWJlcjogJycsDQogICAgICAgICAgICB1cmdlU3RhdHVzOiAnJywNCiAgICAgICAgICAgIHRyYWNraW5nVGltZTogJycsDQogICAgICAgICAgICB1cmdlRGVzY3JpYmU6ICcnLA0KICAgICAgICAgICAgc3RhdHVzOiAyLA0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLmxpdGlnYXRpb25Mb2cgPSB7DQogICAgICAgICAgICBsb2FuSWQ6IG5ld1ZhbC7mtYHnqIvluo/lj7csDQogICAgICAgICAgICBsaXRpZ2F0aW9uSWQ6IG5ld1ZhbC7luo/lj7csDQogICAgICAgICAgICBkb2NOYW1lOiAnJywNCiAgICAgICAgICAgIGRvY051bWJlcjogJycsDQogICAgICAgICAgICBkb2NVcGxvYWRVcmw6IFtdLA0KICAgICAgICAgICAgZG9jRWZmZWN0aXZlRGF0ZTogJycsDQogICAgICAgICAgICBvcGVuRGF0ZTogJycsDQogICAgICAgICAgICBzdGF0dXM6ICcnLA0KICAgICAgICAgIH0NCiAgICAgICAgICAvLyDph43nva7liIbmnJ/ooajljZUNCiAgICAgICAgICB0aGlzLmluc3RhbGxtZW50Rm9ybSA9IHsNCiAgICAgICAgICAgIGFwcGx5QW1vdW50OiAwLA0KICAgICAgICAgICAgcGVyaW9kQ291bnQ6IDEsDQogICAgICAgICAgICBiaWxsQW1vdW50OiAnMC4wMCcsDQogICAgICAgICAgICB0YWlsQW1vdW50OiAwLA0KICAgICAgICAgICAgcmVwYXlEYXk6IDEsDQogICAgICAgICAgICB0YWlsUGF5VGltZTogbnVsbCwNCiAgICAgICAgICAgIGFjY291bnRUeXBlOiAnJywNCiAgICAgICAgICAgIGluc3RhbGxtZW50U3RhdHVzOiAyIC8vIDIt5rOV6K+J5YiG5pyfDQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChuZXdWYWwgJiYgbmV3VmFsLua1geeoi+W6j+WPtykgew0KICAgICAgICAgICAgdGhpcy5pbnN0YWxsbWVudEZvcm0ubG9hbklkID0gbmV3VmFsLua1geeoi+W6j+WPtw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIGltbWVkaWF0ZTogdHJ1ZSwNCiAgICAgIGRlZXA6IHRydWUsDQogICAgfSwNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOWIhuacn+ihqOWNleiuoeeul+aWueazlQ0KICAgIGhhbmRsZUluc3RhbGxtZW50Rm9ybUNoYW5nZSgpIHsNCiAgICAgIGNvbnN0IGFwcGx5QW1vdW50ID0gTnVtYmVyKHRoaXMuaW5zdGFsbG1lbnRGb3JtLmFwcGx5QW1vdW50KSB8fCAwDQogICAgICBjb25zdCBwZXJpb2RDb3VudCA9IE51bWJlcih0aGlzLmluc3RhbGxtZW50Rm9ybS5wZXJpb2RDb3VudCkgfHwgMQ0KICAgICAgY29uc3QgdGFpbEFtb3VudCA9IE51bWJlcih0aGlzLmluc3RhbGxtZW50Rm9ybS50YWlsQW1vdW50KSB8fCAwDQogICAgICBpZiAoYXBwbHlBbW91bnQgPj0gMCAmJiBwZXJpb2RDb3VudCA+PSAxKSB7DQogICAgICAgIHRoaXMuaW5zdGFsbG1lbnRGb3JtLmJpbGxBbW91bnQgPSAoKGFwcGx5QW1vdW50IC0gdGFpbEFtb3VudCkgLyBwZXJpb2RDb3VudCkudG9GaXhlZCgyKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5pbnN0YWxsbWVudEZvcm0uYmlsbEFtb3VudCA9ICcwLjAwJw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6YCa55So55qE5LiK5Lyg5oiQ5Yqf5aSE55CG5Ye95pWwDQogICAgaGFuZGxlVXBsb2FkU3VjY2VzcyhyZXMsIGZpbGUsIGZpbGVMaXN0LCBmb3JtRmllbGQpIHsNCiAgICAgIGNvbnN0IFtvYmosIHByb3BdID0gZm9ybUZpZWxkLnNwbGl0KCcuJykNCiAgICAgIHRoaXNbb2JqXVtwcm9wXSA9IGZpbGVMaXN0DQogICAgfSwNCiAgICAvLyDpgJrnlKjnmoTliKDpmaTlpITnkIblh73mlbANCiAgICBoYW5kbGVSZW1vdmUoZmlsZSwgZmlsZUxpc3QsIGZvcm1GaWVsZCkgew0KICAgICAgY29uc3QgW29iaiwgcHJvcF0gPSBmb3JtRmllbGQuc3BsaXQoJy4nKQ0KICAgICAgdGhpc1tvYmpdW3Byb3BdID0gZmlsZUxpc3QNCiAgICB9LA0KICAgIC8vIOS4iuS8oOWksei0pQ0KICAgIGhhbmRsZVVwbG9hZEVycm9yKCkgew0KICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+S4iuS8oOWksei0pe+8jOivt+mHjeivlScpDQogICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKQ0KICAgIH0sDQogICAgLy8g5Zu+54mH6aKE6KeIDQogICAgaGFuZGxlUGljdHVyZUNhcmRQcmV2aWV3KGZpbGUpIHsNCiAgICAgIHRoaXMuZGlhbG9nSW1hZ2VVcmwgPSBmaWxlLnVybA0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQogICAgLyoqIOaPkOS6pOihqOWNlSAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICBjb25zdCBsb2FuUmVtaW5kZXJDb3B5ID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLmxvYW5SZW1pbmRlcikpDQogICAgICBjb25zdCBsaXRpZ2F0aW9uTG9nQ29weSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5saXRpZ2F0aW9uTG9nKSkNCiAgICAgIGxvYW5SZW1pbmRlckNvcHkuZnVuZHNJbWFnZSA9IGxvYW5SZW1pbmRlckNvcHkuZnVuZHNJbWFnZS5tYXAoaXRlbSA9PiBpdGVtLnJlc3BvbnNlKS5qb2luKCcsJykNCiAgICAgIGxpdGlnYXRpb25Mb2dDb3B5LmRvY1VwbG9hZFVybCA9IGxpdGlnYXRpb25Mb2dDb3B5LmRvY1VwbG9hZFVybC5tYXAoaXRlbSA9PiBpdGVtLnJlc3BvbnNlKS5qb2luKCcsJykNCiAgICAgIGxvYW5SZW1pbmRlckNvcHkuZnVuZHNBY2NvdW50VHlwZSA9DQogICAgICAgIGxvYW5SZW1pbmRlckNvcHkuZnVuZHNBY2NvdW50VHlwZSA9PT0gJ+WFtuS7licgPyBsb2FuUmVtaW5kZXJDb3B5LmFjY291bnROdW1iZXIgOiBsb2FuUmVtaW5kZXJDb3B5LmZ1bmRzQWNjb3VudFR5cGUNCiAgICAgIC8vIOWwhuaXpeW/l+aPj+i/sOS7jiBsb2FuUmVtaW5kZXIg5aSN5Yi25YiwIGxpdGlnYXRpb25Mb2cNCiAgICAgIGxpdGlnYXRpb25Mb2dDb3B5LnVyZ2VEZXNjcmliZSA9IGxvYW5SZW1pbmRlckNvcHkudXJnZURlc2NyaWJlDQoNCiAgICAgIC8vIOWmguaenOmAieaLqeS6huWIhuacn+i/mOasvu+8jOWFiOaPkOS6pOWIhuacn+eUs+ivtw0KICAgICAgaWYgKGxvYW5SZW1pbmRlckNvcHkucmVwYXltZW50U3RhdHVzID09PSAnMycpIHsNCiAgICAgICAgdGhpcy5zdWJtaXRJbnN0YWxsbWVudEFwcGxpY2F0aW9uKCkudGhlbigoKSA9PiB7DQogICAgICAgICAgLy8g5YiG5pyf55Sz6K+35o+Q5Lqk5oiQ5Yqf5ZCO77yM5YaN5o+Q5Lqk5pel5b+XDQogICAgICAgICAgdGhpcy5zdWJtaXRMaXRpZ2F0aW9uTG9nRGF0YShsb2FuUmVtaW5kZXJDb3B5LCBsaXRpZ2F0aW9uTG9nQ29weSkNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfliIbmnJ/nlLPor7fmj5DkuqTlpLHotKUnKQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g55u05o6l5o+Q5Lqk5pel5b+XDQogICAgICAgIHRoaXMuc3VibWl0TGl0aWdhdGlvbkxvZ0RhdGEobG9hblJlbWluZGVyQ29weSwgbGl0aWdhdGlvbkxvZ0NvcHkpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDmj5DkuqTliIbmnJ/nlLPor7cgKi8NCiAgICBzdWJtaXRJbnN0YWxsbWVudEFwcGxpY2F0aW9uKCkgew0KICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHsNCiAgICAgICAgLy8g6KGo5Y2V6aqM6K+BDQogICAgICAgIGlmICghdGhpcy5pbnN0YWxsbWVudEZvcm0uYXBwbHlBbW91bnQgfHwgdGhpcy5pbnN0YWxsbWVudEZvcm0uYXBwbHlBbW91bnQgPD0gMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+eUs+ivt+WIhuacn+mHkemineW/hemhu+Wkp+S6jjAnKQ0KICAgICAgICAgIHJlamVjdCgpDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCiAgICAgICAgaWYgKCF0aGlzLmluc3RhbGxtZW50Rm9ybS5wZXJpb2RDb3VudCB8fCB0aGlzLmluc3RhbGxtZW50Rm9ybS5wZXJpb2RDb3VudCA8PSAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5YiG5pyf5pyf5pWw5b+F6aG75aSn5LqOMCcpDQogICAgICAgICAgcmVqZWN0KCkNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgICBpZiAoIXRoaXMuaW5zdGFsbG1lbnRGb3JtLmFjY291bnRUeXBlKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K+36YCJ5oup6LSm5Y+357G75Z6LJykNCiAgICAgICAgICByZWplY3QoKQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6LCD55So5rOV6K+J5YiG5pyf55Sz6K+3QVBJDQogICAgICAgIHRoaXMuJGh0dHAucG9zdCgnL2xpdGlnYXRpb25fY2FzZS9saXRpZ2F0aW9uX2Nhc2UvaW5zdGFsbG1lbnQnLCB0aGlzLmluc3RhbGxtZW50Rm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5YiG5pyf55Sz6K+35o+Q5Lqk5oiQ5YqfJykNCiAgICAgICAgICAgIHJlc29sdmUoKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5YiG5pyf55Sz6K+35o+Q5Lqk5aSx6LSl77yaJyArIHJlc3BvbnNlLm1zZykNCiAgICAgICAgICAgIHJlamVjdCgpDQogICAgICAgICAgfQ0KICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign5YiG5pyf55Sz6K+35o+Q5Lqk5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfliIbmnJ/nlLPor7fmj5DkuqTlpLHotKUnKQ0KICAgICAgICAgIHJlamVjdCgpDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvKiog5o+Q5Lqk5rOV6K+J5pel5b+X5pWw5o2uICovDQogICAgc3VibWl0TGl0aWdhdGlvbkxvZ0RhdGEobG9hblJlbWluZGVyQ29weSwgbGl0aWdhdGlvbkxvZ0NvcHkpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfmj5DkuqTooajljZXmlbDmja7vvJonLCBsb2FuUmVtaW5kZXJDb3B5KQ0KICAgICAgY29uc29sZS5sb2coJ+aPkOS6pOihqOWNleaVsOaNru+8micsIGxpdGlnYXRpb25Mb2dDb3B5KQ0KICAgICAgc3VibWl0TGl0aWdhdGlvbkxvZyh7IGxvYW5SZW1pbmRlcjogbG9hblJlbWluZGVyQ29weSwgbGl0aWdhdGlvbkxvZzogbGl0aWdhdGlvbkxvZ0NvcHkgfSkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfmj5DkuqTmiJDlip8nKQ0KICAgICAgICB0aGlzLnZpc2libGUgPSBmYWxzZQ0KICAgICAgICB0aGlzLnJlc2V0Rm9ybSgpDQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOWPlua2iOaTjeS9nCAqLw0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMudmlzaWJsZSA9IGZhbHNlDQogICAgICB0aGlzLnJlc2V0Rm9ybSgpDQogICAgICByZXR1cm4NCiAgICB9LA0KICAgIC8qKiDph43nva7ooajljZUgKi8NCiAgICByZXNldEZvcm0oKSB7DQogICAgICB0aGlzLmxvYW5SZW1pbmRlciA9IHsNCiAgICAgICAgZnVuZHNJbWFnZTogW10sDQogICAgICB9DQogICAgICB0aGlzLmxpdGlnYXRpb25Mb2cgPSB7DQogICAgICAgIGRvY1VwbG9hZFVybDogW10sDQogICAgICB9DQogICAgICB0aGlzLmluc3RhbGxtZW50Rm9ybSA9IHsNCiAgICAgICAgYXBwbHlBbW91bnQ6IDAsDQogICAgICAgIHBlcmlvZENvdW50OiAxLA0KICAgICAgICBiaWxsQW1vdW50OiAnMC4wMCcsDQogICAgICAgIHRhaWxBbW91bnQ6IDAsDQogICAgICAgIHJlcGF5RGF5OiAxLA0KICAgICAgICB0YWlsUGF5VGltZTogbnVsbCwNCiAgICAgICAgYWNjb3VudFR5cGU6ICcnLA0KICAgICAgICBpbnN0YWxsbWVudFN0YXR1czogMiAvLyAyLeazleivieWIhuacnw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOe7n+S4gOaJk+W8gOW8ueeql+eahOaWueazlSAqLw0KICAgIG9wZW5EaWFsb2coKSB7DQogICAgICB0aGlzLnZpc2libGUgPSB0cnVlDQogICAgICByZXR1cm4NCiAgICB9LA0KICAgIC8qKiDlpITnkIbmlofku7botoXlh7rpmZDliLYgKi8NCiAgICBoYW5kbGVFeGNlZWQoZmlsZXMsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+WPquiDveS4iuS8oOS4gOS4quaWh+S7ticpDQogICAgICByZXR1cm4NCiAgICB9LA0KICB9LA0KfQ0K"}, {"version": 3, "sources": ["litigationLogForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8SA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "litigationLogForm.vue", "sourceRoot": "src/views/litigation/litigation/modules", "sourcesContent": ["<template>\r\n  <el-dialog :title=\"title\" :visible.sync=\"visible\" width=\"800px\" append-to-body @close=\"resetForm\">\r\n    <el-form ref=\"form\" :model=\"loanReminder\" label-width=\"120px\">\r\n      <!-- 第一部分：文书相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-document\"></i>\r\n        贷款信息\r\n      </el-divider>\r\n      <!-- 非填入字段 -->\r\n      <el-descriptions title=\"\" :column=\"3\" border>\r\n        <el-descriptions-item label=\"贷款人\">\r\n          {{ loanReminder.customerName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"出单渠道\">\r\n          {{ loanReminder.channel }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"放款银行\">\r\n          {{ loanReminder.bank }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <!-- 第一部分：文书相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-document\"></i>\r\n        文书信息\r\n      </el-divider>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"变更法诉状态\">\r\n            <litigation-status v-model=\"litigationLog.status\" placeholder=\"请选择法诉状态\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书名称\">\r\n            <el-select v-model=\"litigationLog.docName\" placeholder=\"请选择文书名称\" style=\"width: 100%\">\r\n              <el-option label=\"诉前调号\" value=\"诉前调号\" />\r\n              <el-option label=\"民初号\" value=\"民初号\" />\r\n              <el-option label=\"执行号\" value=\"执行号\" />\r\n              <el-option label=\"执保号\" value=\"执保号\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书号\">\r\n            <el-input v-model=\"litigationLog.docNumber\" placeholder=\"请输入文书号\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书生效\">\r\n            <el-date-picker v-model=\"litigationLog.docEffectiveDate\" type=\"date\" placeholder=\"选择日期\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\" v-if=\"litigationLog.status === '待出法院文书'\">\r\n          <el-form-item label=\"登记开庭时间\">\r\n            <el-date-picker v-model=\"litigationLog.openDate\" type=\"datetime\" placeholder=\"选择开庭时间\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"上传文书\">\r\n            <el-upload\r\n              :data=\"data\"\r\n              :action=\"uploadUrl\"\r\n              :headers=\"headers\"\r\n              :limit=\"1\"\r\n              :file-list=\"litigationLog.docUploadUrl\"\r\n              :on-success=\"(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'litigationLog.docUploadUrl')\"\r\n              :on-remove=\"(file, fileList) => handleRemove(file, fileList, 'litigationLog.docUploadUrl')\"\r\n              :on-error=\"handleUploadError\">\r\n              <el-button size=\"small\" type=\"primary\" :disabled=\"litigationLog.docUploadUrl.length >= 1\">点击上传</el-button>\r\n            </el-upload>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第二部分：还款相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-money\"></i>\r\n        还款信息\r\n      </el-divider>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"还款类型\">\r\n            <el-select v-model=\"loanReminder.repaymentStatus\" placeholder=\"请选择还款类型\" style=\"width: 100%\">\r\n              <el-option label=\"部分还款\" value=\"2\" />\r\n              <el-option label=\"分期还款\" value=\"3\" />\r\n              <el-option label=\"协商买车\" value=\"4\" />\r\n              <el-option label=\"法诉结清\" value=\"5\" />\r\n              <el-option label=\"法诉减免结清\" value=\"6\" />\r\n              <el-option label=\"拍卖回款\" value=\"7\" />\r\n              <el-option label=\"法院划扣\" value=\"8\" />\r\n              <el-option label=\"其他分配回款\" value=\"9\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"款项明细类型\">\r\n            <el-select v-model=\"loanReminder.fundsRepayment\" placeholder=\"请选择款项明细类型\" style=\"width: 100%\">\r\n              <el-option label=\"律师费\" value=\"律师费\" />\r\n              <el-option label=\"法诉费\" value=\"法诉费\" />\r\n              <el-option label=\"保全费\" value=\"保全费\" />\r\n              <el-option label=\"布控费\" value=\"布控费\" />\r\n              <el-option label=\"公告费\" value=\"公告费\" />\r\n              <el-option label=\"评估费\" value=\"评估费\" />\r\n              <el-option label=\"执行费\" value=\"执行费\" />\r\n              <el-option label=\"违约金\" value=\"违约金\" />\r\n              <el-option label=\"担保费\" value=\"担保费\" />\r\n              <el-option label=\"居间费\" value=\"居间费\" />\r\n              <el-option label=\"代偿金\" value=\"代偿金\" />\r\n              <el-option label=\"判决金额\" value=\"判决金额\" />\r\n              <el-option label=\"利息\" value=\"利息\" />\r\n              <el-option label=\"其他欠款\" value=\"其他欠款\" />\r\n              <el-option label=\"保险费\" value=\"保险费\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"金额\">\r\n            <el-input-number v-model=\"loanReminder.fundsAmount\" :min=\"0\" :precision=\"2\" style=\"width: 100%\" placeholder=\"请输入金额\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"账号类型\">\r\n            <el-select v-model=\"loanReminder.fundsAccountType\" placeholder=\"请选择账号类型\" style=\"width: 100%\">\r\n              <el-option label=\"银行账户\" value=\"银行账户\" />\r\n              <el-option label=\"微信\" value=\"微信\" />\r\n              <el-option label=\"支付宝\" value=\"支付宝\" />\r\n              <el-option label=\"其他\" value=\"其他\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"账号\">\r\n            <el-input v-model=\"loanReminder.accountNumber\" :disabled=\"loanReminder.fundsAccountType !== '其他'\" placeholder=\"请输入账号\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"还款凭据\">\r\n            <el-upload\r\n              :data=\"data\"\r\n              :action=\"uploadUrl\"\r\n              :headers=\"headers\"\r\n              list-type=\"picture-card\"\r\n              :file-list=\"loanReminder.fundsImage\"\r\n              :on-preview=\"handlePictureCardPreview\"\r\n              :on-success=\"(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'loanReminder.fundsImage')\"\r\n              :on-remove=\"(file, fileList) => handleRemove(file, fileList, 'loanReminder.fundsImage')\"\r\n              :on-error=\"handleUploadError\">\r\n              <i class=\"el-icon-plus\"></i>\r\n            </el-upload>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 分期表单 - 当选择分期还款时显示 -->\r\n      <div v-if=\"loanReminder.repaymentStatus === '3'\">\r\n        <el-divider content-position=\"left\">\r\n          <i class=\"el-icon-s-order\"></i>\r\n          分期申请信息\r\n        </el-divider>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"申请分期金额\">\r\n              <el-input-number\r\n                v-model=\"installmentForm.applyAmount\"\r\n                :min=\"0\"\r\n                :precision=\"2\"\r\n                :step=\"100\"\r\n                :controls-position=\"'right'\"\r\n                placeholder=\"请输入申请分期金额\"\r\n                @input=\"handleInstallmentFormChange\"\r\n                style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"分期期数\">\r\n              <el-input-number\r\n                v-model=\"installmentForm.periodCount\"\r\n                :min=\"1\"\r\n                :precision=\"0\"\r\n                :step=\"1\"\r\n                :controls-position=\"'right'\"\r\n                placeholder=\"请输入分期期数\"\r\n                @input=\"handleInstallmentFormChange\"\r\n                style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"每期账单金额\">\r\n              <el-input v-model=\"installmentForm.billAmount\" placeholder=\"每期账单金额\" disabled />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"每期还款日\">\r\n              <el-select v-model=\"installmentForm.repayDay\" placeholder=\"请选择每期还款日\" style=\"width: 100%\">\r\n                <el-option v-for=\"day in 28\" :key=\"day\" :label=\"`每月${day}号`\" :value=\"day\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"尾款金额\">\r\n              <el-input-number\r\n                v-model=\"installmentForm.tailAmount\"\r\n                :min=\"0\"\r\n                :precision=\"2\"\r\n                :step=\"100\"\r\n                :controls-position=\"'right'\"\r\n                placeholder=\"请输入尾款金额\"\r\n                @input=\"handleInstallmentFormChange\"\r\n                style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"尾款支付时间\">\r\n              <el-date-picker\r\n                v-model=\"installmentForm.tailPayTime\"\r\n                type=\"date\"\r\n                placeholder=\"选择尾款支付时间\"\r\n                style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\">\r\n              <el-select v-model=\"installmentForm.accountType\" placeholder=\"请选择账号类型\" style=\"width: 100%\">\r\n                <el-option label=\"银行账户\" value=\"银行账户\" />\r\n                <el-option label=\"微信\" value=\"微信\" />\r\n                <el-option label=\"支付宝\" value=\"支付宝\" />\r\n                <el-option label=\"其他\" value=\"其他\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 第三部分：日志相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-notebook-2\"></i>\r\n        日志信息\r\n      </el-divider>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"日志类型\">\r\n            <el-select v-model=\"loanReminder.urgeStatus\" placeholder=\"请选择日志类型\" style=\"width: 100%\">\r\n              <el-option label=\"继续跟踪\" value=\"1\" />\r\n              <el-option label=\"约定还款\" value=\"2\" />\r\n              <el-option label=\"无法跟进\" value=\"3\" />\r\n              <el-option label=\"暂时无需跟进\" value=\"4\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"下次跟进时间\">\r\n            <el-date-picker v-model=\"loanReminder.trackingTime\" type=\"datetime\" placeholder=\"选择跟进时间\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"日志描述\">\r\n            <el-input v-model=\"loanReminder.urgeDescribe\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入日志描述\" maxlength=\"500\" show-word-limit />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"cancel\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n    </div>\r\n    <!-- 图片预览 -->\r\n    <el-dialog :visible.sync=\"dialogVisible\">\r\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt=\"\" />\r\n    </el-dialog>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport litigationStatus from '@/layout/components/Dialog/litigationStatus.vue'\r\nimport { getToken } from '@/utils/auth'\r\nimport { submitLitigationLog } from '@/api/litigation/litigation'\r\n\r\nexport default {\r\n  name: 'LitigationLogForm',\r\n  components: {\r\n    litigationStatus,\r\n  },\r\n  props: {\r\n    action: {\r\n      type: String,\r\n      default: '/common/ossupload',\r\n    },\r\n    data: {\r\n      type: Object,\r\n      default: () => {},\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      title: '提交法诉日志',\r\n      visible: false,\r\n      loanReminder: {},\r\n      litigationLog: {},\r\n      installmentForm: {\r\n        applyAmount: 0,\r\n        periodCount: 1,\r\n        billAmount: '0.00',\r\n        tailAmount: 0,\r\n        repayDay: 1,\r\n        tailPayTime: null,\r\n        accountType: '',\r\n        installmentStatus: 2 // 2-法诉分期\r\n      },\r\n      uploadUrl: process.env.VUE_APP_BASE_API + this.action,\r\n      headers: {\r\n        Authorization: 'Bearer ' + getToken(),\r\n      },\r\n      dialogImageUrl: '',\r\n      dialogVisible: false,\r\n    }\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          console.log('newVal', newVal)\r\n          this.loanReminder = {\r\n            loanId: newVal.流程序号,\r\n            customerName: newVal.贷款人,\r\n            channel: newVal.出单渠道,\r\n            bank: newVal.放款银行,\r\n            identity: this.$store.state.user.roles[0],\r\n            repaymentStatus: '',\r\n            fundsRepayment: '',\r\n            fundsAmount: '',\r\n            fundsImage: [],\r\n            fundsAccountType: '',\r\n            accountNumber: '',\r\n            urgeStatus: '',\r\n            trackingTime: '',\r\n            urgeDescribe: '',\r\n            status: 2,\r\n          }\r\n          this.litigationLog = {\r\n            loanId: newVal.流程序号,\r\n            litigationId: newVal.序号,\r\n            docName: '',\r\n            docNumber: '',\r\n            docUploadUrl: [],\r\n            docEffectiveDate: '',\r\n            openDate: '',\r\n            status: '',\r\n          }\r\n          // 重置分期表单\r\n          this.installmentForm = {\r\n            applyAmount: 0,\r\n            periodCount: 1,\r\n            billAmount: '0.00',\r\n            tailAmount: 0,\r\n            repayDay: 1,\r\n            tailPayTime: null,\r\n            accountType: '',\r\n            installmentStatus: 2 // 2-法诉分期\r\n          }\r\n          if (newVal && newVal.流程序号) {\r\n            this.installmentForm.loanId = newVal.流程序号\r\n          }\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true,\r\n    },\r\n  },\r\n  methods: {\r\n    // 分期表单计算方法\r\n    handleInstallmentFormChange() {\r\n      const applyAmount = Number(this.installmentForm.applyAmount) || 0\r\n      const periodCount = Number(this.installmentForm.periodCount) || 1\r\n      const tailAmount = Number(this.installmentForm.tailAmount) || 0\r\n      if (applyAmount >= 0 && periodCount >= 1) {\r\n        this.installmentForm.billAmount = ((applyAmount - tailAmount) / periodCount).toFixed(2)\r\n      } else {\r\n        this.installmentForm.billAmount = '0.00'\r\n      }\r\n    },\r\n    // 通用的上传成功处理函数\r\n    handleUploadSuccess(res, file, fileList, formField) {\r\n      const [obj, prop] = formField.split('.')\r\n      this[obj][prop] = fileList\r\n    },\r\n    // 通用的删除处理函数\r\n    handleRemove(file, fileList, formField) {\r\n      const [obj, prop] = formField.split('.')\r\n      this[obj][prop] = fileList\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError('上传失败，请重试')\r\n      this.$modal.closeLoading()\r\n    },\r\n    // 图片预览\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url\r\n      this.dialogVisible = true\r\n    },\r\n    /** 提交表单 */\r\n    submitForm() {\r\n      const loanReminderCopy = JSON.parse(JSON.stringify(this.loanReminder))\r\n      const litigationLogCopy = JSON.parse(JSON.stringify(this.litigationLog))\r\n      loanReminderCopy.fundsImage = loanReminderCopy.fundsImage.map(item => item.response).join(',')\r\n      litigationLogCopy.docUploadUrl = litigationLogCopy.docUploadUrl.map(item => item.response).join(',')\r\n      loanReminderCopy.fundsAccountType =\r\n        loanReminderCopy.fundsAccountType === '其他' ? loanReminderCopy.accountNumber : loanReminderCopy.fundsAccountType\r\n      // 将日志描述从 loanReminder 复制到 litigationLog\r\n      litigationLogCopy.urgeDescribe = loanReminderCopy.urgeDescribe\r\n\r\n      // 如果选择了分期还款，先提交分期申请\r\n      if (loanReminderCopy.repaymentStatus === '3') {\r\n        this.submitInstallmentApplication().then(() => {\r\n          // 分期申请提交成功后，再提交日志\r\n          this.submitLitigationLogData(loanReminderCopy, litigationLogCopy)\r\n        }).catch(() => {\r\n          this.$modal.msgError('分期申请提交失败')\r\n        })\r\n      } else {\r\n        // 直接提交日志\r\n        this.submitLitigationLogData(loanReminderCopy, litigationLogCopy)\r\n      }\r\n    },\r\n\r\n    /** 提交分期申请 */\r\n    submitInstallmentApplication() {\r\n      return new Promise((resolve, reject) => {\r\n        // 表单验证\r\n        if (!this.installmentForm.applyAmount || this.installmentForm.applyAmount <= 0) {\r\n          this.$message.error('申请分期金额必须大于0')\r\n          reject()\r\n          return\r\n        }\r\n        if (!this.installmentForm.periodCount || this.installmentForm.periodCount <= 0) {\r\n          this.$message.error('分期期数必须大于0')\r\n          reject()\r\n          return\r\n        }\r\n        if (!this.installmentForm.accountType) {\r\n          this.$message.error('请选择账号类型')\r\n          reject()\r\n          return\r\n        }\r\n\r\n        // 调用法诉分期申请API\r\n        this.$http.post('/litigation_case/litigation_case/installment', this.installmentForm).then(response => {\r\n          if (response.code === 200) {\r\n            this.$modal.msgSuccess('分期申请提交成功')\r\n            resolve()\r\n          } else {\r\n            this.$modal.msgError('分期申请提交失败：' + response.msg)\r\n            reject()\r\n          }\r\n        }).catch(error => {\r\n          console.error('分期申请提交失败:', error)\r\n          this.$modal.msgError('分期申请提交失败')\r\n          reject()\r\n        })\r\n      })\r\n    },\r\n\r\n    /** 提交法诉日志数据 */\r\n    submitLitigationLogData(loanReminderCopy, litigationLogCopy) {\r\n      console.log('提交表单数据：', loanReminderCopy)\r\n      console.log('提交表单数据：', litigationLogCopy)\r\n      submitLitigationLog({ loanReminder: loanReminderCopy, litigationLog: litigationLogCopy }).then(res => {\r\n        this.$modal.msgSuccess('提交成功')\r\n        this.visible = false\r\n        this.resetForm()\r\n      })\r\n    },\r\n    /** 取消操作 */\r\n    cancel() {\r\n      this.visible = false\r\n      this.resetForm()\r\n      return\r\n    },\r\n    /** 重置表单 */\r\n    resetForm() {\r\n      this.loanReminder = {\r\n        fundsImage: [],\r\n      }\r\n      this.litigationLog = {\r\n        docUploadUrl: [],\r\n      }\r\n      this.installmentForm = {\r\n        applyAmount: 0,\r\n        periodCount: 1,\r\n        billAmount: '0.00',\r\n        tailAmount: 0,\r\n        repayDay: 1,\r\n        tailPayTime: null,\r\n        accountType: '',\r\n        installmentStatus: 2 // 2-法诉分期\r\n      }\r\n    },\r\n    /** 统一打开弹窗的方法 */\r\n    openDialog() {\r\n      this.visible = true\r\n      return\r\n    },\r\n    /** 处理文件超出限制 */\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning('只能上传一个文件')\r\n      return\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.el-divider__text {\r\n  padding: 0 15px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.el-upload {\r\n  width: 100%;\r\n}\r\n</style>\r\n"]}]}