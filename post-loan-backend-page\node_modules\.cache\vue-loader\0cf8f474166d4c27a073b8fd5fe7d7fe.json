{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogForm.vue", "mtime": 1754377820786}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgbGl0aWdhdGlvblN0YXR1cyBmcm9tICdAL2xheW91dC9jb21wb25lbnRzL0RpYWxvZy9saXRpZ2F0aW9uU3RhdHVzLnZ1ZScNCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAnQC91dGlscy9hdXRoJw0KaW1wb3J0IHsgc3VibWl0TGl0aWdhdGlvbkxvZyB9IGZyb20gJ0AvYXBpL2xpdGlnYXRpb24vbGl0aWdhdGlvbicNCmltcG9ydCB7IGFkZEluc3RhbGxtZW50X2FwcGxpY2F0aW9uX2F1ZGl0IH0gZnJvbSAnQC9hcGkvaW5zdGFsbG1lbnRfYXBwbGljYXRpb25fYXVkaXQvaW5zdGFsbG1lbnRfYXBwbGljYXRpb25fYXVkaXQnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0xpdGlnYXRpb25Mb2dGb3JtJywNCiAgY29tcG9uZW50czogew0KICAgIGxpdGlnYXRpb25TdGF0dXMsDQogIH0sDQogIHByb3BzOiB7DQogICAgYWN0aW9uOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnL2NvbW1vbi9vc3N1cGxvYWQnLA0KICAgIH0sDQogICAgZGF0YTogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4ge30sDQogICAgfSwNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdGl0bGU6ICfmj5DkuqTms5Xor4nml6Xlv5cnLA0KICAgICAgdmlzaWJsZTogZmFsc2UsDQogICAgICBsb2FuUmVtaW5kZXI6IHt9LA0KICAgICAgbGl0aWdhdGlvbkxvZzoge30sDQogICAgICBpbnN0YWxsbWVudEZvcm06IHsNCiAgICAgICAgbG9hbklkOiBudWxsLA0KICAgICAgICBhcHBseUFtb3VudDogMCwNCiAgICAgICAgcGVyaW9kQ291bnQ6IDEsDQogICAgICAgIGJpbGxBbW91bnQ6ICcwLjAwJywNCiAgICAgICAgdGFpbEFtb3VudDogMCwNCiAgICAgICAgcmVwYXlEYXk6IDEsDQogICAgICAgIHRhaWxQYXlUaW1lOiBudWxsLA0KICAgICAgICBhY2NvdW50VHlwZTogJycsDQogICAgICAgIGluc3RhbGxtZW50U3RhdHVzOiAyIC8vIDIt5rOV6K+J5YiG5pyfDQogICAgICB9LA0KICAgICAgLy8g5YiG5pyf6KGo5Y2V6aqM6K+B6KeE5YiZDQogICAgICBpbnN0YWxsbWVudFJ1bGVzOiB7DQogICAgICAgIGFwcGx5QW1vdW50OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeeUs+ivt+WIhuacn+mHkeminScsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgdHlwZTogJ251bWJlcicsIG1pbjogMC4wMSwgbWVzc2FnZTogJ+eUs+ivt+WIhuacn+mHkemineW/hemhu+Wkp+S6jjAnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBwZXJpb2RDb3VudDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXliIbmnJ/mnJ/mlbAnLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgICB7IHR5cGU6ICdudW1iZXInLCBtaW46IDEsIG1heDogNjAsIG1lc3NhZ2U6ICfliIbmnJ/mnJ/mlbDlv4XpobvlnKgxLTYw5pyf5LmL6Ze0JywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgcmVwYXlEYXk6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5q+P5pyf6L+Y5qy+5pelJywgdHJpZ2dlcjogJ2NoYW5nZScgfQ0KICAgICAgICBdLA0KICAgICAgICBhY2NvdW50VHlwZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6notKblj7fnsbvlnosnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0sDQogICAgICAgIHRhaWxQYXlUaW1lOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdmFsaWRhdG9yOiAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICAgICAgICAgIGlmICh0aGlzLmluc3RhbGxtZW50Rm9ybS50YWlsQW1vdW50ID4gMCAmJiAhdmFsdWUpIHsNCiAgICAgICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+acieWwvuasvuaXtuW/hemhu+mAieaLqeWwvuasvuaUr+S7mOaXtumXtCcpKQ0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIGNhbGxiYWNrKCkNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHRyaWdnZXI6ICdjaGFuZ2UnDQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgdXBsb2FkVXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgdGhpcy5hY3Rpb24sDQogICAgICBoZWFkZXJzOiB7DQogICAgICAgIEF1dGhvcml6YXRpb246ICdCZWFyZXIgJyArIGdldFRva2VuKCksDQogICAgICB9LA0KICAgICAgZGlhbG9nSW1hZ2VVcmw6ICcnLA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgIGRhdGE6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsKSB7DQogICAgICAgIGlmIChuZXdWYWwpIHsNCiAgICAgICAgICBjb25zb2xlLmxvZygnbmV3VmFsJywgbmV3VmFsKQ0KICAgICAgICAgIHRoaXMubG9hblJlbWluZGVyID0gew0KICAgICAgICAgICAgbG9hbklkOiBuZXdWYWwu5rWB56iL5bqP5Y+3LA0KICAgICAgICAgICAgY3VzdG9tZXJOYW1lOiBuZXdWYWwu6LS35qy+5Lq6LA0KICAgICAgICAgICAgY2hhbm5lbDogbmV3VmFsLuWHuuWNlea4oOmBkywNCiAgICAgICAgICAgIGJhbms6IG5ld1ZhbC7mlL7mrL7pk7booYwsDQogICAgICAgICAgICBpZGVudGl0eTogdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlc1swXSwNCiAgICAgICAgICAgIHJlcGF5bWVudFN0YXR1czogJycsDQogICAgICAgICAgICBmdW5kc1JlcGF5bWVudDogJycsDQogICAgICAgICAgICBmdW5kc0Ftb3VudDogJycsDQogICAgICAgICAgICBmdW5kc0ltYWdlOiBbXSwNCiAgICAgICAgICAgIGZ1bmRzQWNjb3VudFR5cGU6ICcnLA0KICAgICAgICAgICAgYWNjb3VudE51bWJlcjogJycsDQogICAgICAgICAgICB1cmdlU3RhdHVzOiAnJywNCiAgICAgICAgICAgIHRyYWNraW5nVGltZTogJycsDQogICAgICAgICAgICB1cmdlRGVzY3JpYmU6ICcnLA0KICAgICAgICAgICAgc3RhdHVzOiAyLA0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLmxpdGlnYXRpb25Mb2cgPSB7DQogICAgICAgICAgICBsb2FuSWQ6IG5ld1ZhbC7mtYHnqIvluo/lj7csDQogICAgICAgICAgICBsaXRpZ2F0aW9uSWQ6IG5ld1ZhbC7luo/lj7csDQogICAgICAgICAgICBkb2NOYW1lOiAnJywNCiAgICAgICAgICAgIGRvY051bWJlcjogJycsDQogICAgICAgICAgICBkb2NVcGxvYWRVcmw6IFtdLA0KICAgICAgICAgICAgZG9jRWZmZWN0aXZlRGF0ZTogJycsDQogICAgICAgICAgICBvcGVuRGF0ZTogJycsDQogICAgICAgICAgICBzdGF0dXM6ICcnLA0KICAgICAgICAgIH0NCiAgICAgICAgICAvLyDph43nva7liIbmnJ/ooajljZUNCiAgICAgICAgICB0aGlzLmluc3RhbGxtZW50Rm9ybSA9IHsNCiAgICAgICAgICAgIGxvYW5JZDogbmV3VmFsICYmIG5ld1ZhbC7mtYHnqIvluo/lj7cgPyBuZXdWYWwu5rWB56iL5bqP5Y+3IDogbnVsbCwNCiAgICAgICAgICAgIGFwcGx5QW1vdW50OiAwLA0KICAgICAgICAgICAgcGVyaW9kQ291bnQ6IDEsDQogICAgICAgICAgICBiaWxsQW1vdW50OiAnMC4wMCcsDQogICAgICAgICAgICB0YWlsQW1vdW50OiAwLA0KICAgICAgICAgICAgcmVwYXlEYXk6IDEsDQogICAgICAgICAgICB0YWlsUGF5VGltZTogbnVsbCwNCiAgICAgICAgICAgIGFjY291bnRUeXBlOiAnJywNCiAgICAgICAgICAgIGluc3RhbGxtZW50U3RhdHVzOiAyIC8vIDIt5rOV6K+J5YiG5pyfDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgaW1tZWRpYXRlOiB0cnVlLA0KICAgICAgZGVlcDogdHJ1ZSwNCiAgICB9LA0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g5YiG5pyf6KGo5Y2V6K6h566X5pa55rOVDQogICAgaGFuZGxlSW5zdGFsbG1lbnRGb3JtQ2hhbmdlKCkgew0KICAgICAgY29uc3QgYXBwbHlBbW91bnQgPSBOdW1iZXIodGhpcy5pbnN0YWxsbWVudEZvcm0uYXBwbHlBbW91bnQpIHx8IDANCiAgICAgIGNvbnN0IHBlcmlvZENvdW50ID0gTnVtYmVyKHRoaXMuaW5zdGFsbG1lbnRGb3JtLnBlcmlvZENvdW50KSB8fCAxDQogICAgICBjb25zdCB0YWlsQW1vdW50ID0gTnVtYmVyKHRoaXMuaW5zdGFsbG1lbnRGb3JtLnRhaWxBbW91bnQpIHx8IDANCiAgICAgIGlmIChhcHBseUFtb3VudCA+PSAwICYmIHBlcmlvZENvdW50ID49IDEpIHsNCiAgICAgICAgdGhpcy5pbnN0YWxsbWVudEZvcm0uYmlsbEFtb3VudCA9ICgoYXBwbHlBbW91bnQgLSB0YWlsQW1vdW50KSAvIHBlcmlvZENvdW50KS50b0ZpeGVkKDIpDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmluc3RhbGxtZW50Rm9ybS5iaWxsQW1vdW50ID0gJzAuMDAnDQogICAgICB9DQogICAgfSwNCiAgICAvLyDpgJrnlKjnmoTkuIrkvKDmiJDlip/lpITnkIblh73mlbANCiAgICBoYW5kbGVVcGxvYWRTdWNjZXNzKHJlcywgZmlsZSwgZmlsZUxpc3QsIGZvcm1GaWVsZCkgew0KICAgICAgY29uc3QgW29iaiwgcHJvcF0gPSBmb3JtRmllbGQuc3BsaXQoJy4nKQ0KICAgICAgdGhpc1tvYmpdW3Byb3BdID0gZmlsZUxpc3QNCiAgICB9LA0KICAgIC8vIOmAmueUqOeahOWIoOmZpOWkhOeQhuWHveaVsA0KICAgIGhhbmRsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCwgZm9ybUZpZWxkKSB7DQogICAgICBjb25zdCBbb2JqLCBwcm9wXSA9IGZvcm1GaWVsZC5zcGxpdCgnLicpDQogICAgICB0aGlzW29ial1bcHJvcF0gPSBmaWxlTGlzdA0KICAgIH0sDQogICAgLy8g5LiK5Lyg5aSx6LSlDQogICAgaGFuZGxlVXBsb2FkRXJyb3IoKSB7DQogICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5LiK5Lyg5aSx6LSl77yM6K+36YeN6K+VJykNCiAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpDQogICAgfSwNCiAgICAvLyDlm77niYfpooTop4gNCiAgICBoYW5kbGVQaWN0dXJlQ2FyZFByZXZpZXcoZmlsZSkgew0KICAgICAgdGhpcy5kaWFsb2dJbWFnZVVybCA9IGZpbGUudXJsDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgfSwNCiAgICAvKiog5o+Q5Lqk6KGo5Y2VICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIGNvbnN0IGxvYW5SZW1pbmRlckNvcHkgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMubG9hblJlbWluZGVyKSkNCiAgICAgIGNvbnN0IGxpdGlnYXRpb25Mb2dDb3B5ID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLmxpdGlnYXRpb25Mb2cpKQ0KICAgICAgbG9hblJlbWluZGVyQ29weS5mdW5kc0ltYWdlID0gbG9hblJlbWluZGVyQ29weS5mdW5kc0ltYWdlLm1hcChpdGVtID0+IGl0ZW0ucmVzcG9uc2UpLmpvaW4oJywnKQ0KICAgICAgbGl0aWdhdGlvbkxvZ0NvcHkuZG9jVXBsb2FkVXJsID0gbGl0aWdhdGlvbkxvZ0NvcHkuZG9jVXBsb2FkVXJsLm1hcChpdGVtID0+IGl0ZW0ucmVzcG9uc2UpLmpvaW4oJywnKQ0KICAgICAgbG9hblJlbWluZGVyQ29weS5mdW5kc0FjY291bnRUeXBlID0NCiAgICAgICAgbG9hblJlbWluZGVyQ29weS5mdW5kc0FjY291bnRUeXBlID09PSAn5YW25LuWJyA/IGxvYW5SZW1pbmRlckNvcHkuYWNjb3VudE51bWJlciA6IGxvYW5SZW1pbmRlckNvcHkuZnVuZHNBY2NvdW50VHlwZQ0KICAgICAgLy8g5bCG5pel5b+X5o+P6L+w5LuOIGxvYW5SZW1pbmRlciDlpI3liLbliLAgbGl0aWdhdGlvbkxvZw0KICAgICAgbGl0aWdhdGlvbkxvZ0NvcHkudXJnZURlc2NyaWJlID0gbG9hblJlbWluZGVyQ29weS51cmdlRGVzY3JpYmUNCg0KICAgICAgLy8g5aaC5p6c6YCJ5oup5LqG5YiG5pyf6L+Y5qy+77yM5YWI5o+Q5Lqk5YiG5pyf55Sz6K+3DQogICAgICBpZiAobG9hblJlbWluZGVyQ29weS5yZXBheW1lbnRTdGF0dXMgPT09ICczJykgew0KICAgICAgICAvLyDmmL7npLrnoa7orqTlr7nor53moYYNCiAgICAgICAgY29uc3QgY29uZmlybU1lc3NhZ2UgPSBg56Gu6K6k5o+Q5Lqk5YiG5pyf55Sz6K+377yfXG7nlLPor7fph5Hpop3vvJoke3RoaXMuaW5zdGFsbG1lbnRGb3JtLmFwcGx5QW1vdW50feWFg1xu5YiG5pyf5pyf5pWw77yaJHt0aGlzLmluc3RhbGxtZW50Rm9ybS5wZXJpb2RDb3VudH3mnJ9cbuavj+acn+mHkemine+8miR7dGhpcy5pbnN0YWxsbWVudEZvcm0uYmlsbEFtb3VudH3lhYNgDQogICAgICAgIHRoaXMuJGNvbmZpcm0oY29uZmlybU1lc3NhZ2UsICfnoa7orqTliIbmnJ/nlLPor7cnLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrprmj5DkuqQnLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLnN1Ym1pdEluc3RhbGxtZW50QXBwbGljYXRpb24oKS50aGVuKCgpID0+IHsNCiAgICAgICAgICAgIC8vIOWIhuacn+eUs+ivt+aPkOS6pOaIkOWKn+WQju+8jOWGjeaPkOS6pOaXpeW/lw0KICAgICAgICAgICAgdGhpcy5zdWJtaXRMaXRpZ2F0aW9uTG9nRGF0YShsb2FuUmVtaW5kZXJDb3B5LCBsaXRpZ2F0aW9uTG9nQ29weSkNCiAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5YiG5pyf55Sz6K+35o+Q5Lqk5aSx6LSlJykNCiAgICAgICAgICB9KQ0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgLy8g55So5oi35Y+W5raI5LqG5pON5L2cDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDnm7TmjqXmj5DkuqTml6Xlv5cNCiAgICAgICAgdGhpcy5zdWJtaXRMaXRpZ2F0aW9uTG9nRGF0YShsb2FuUmVtaW5kZXJDb3B5LCBsaXRpZ2F0aW9uTG9nQ29weSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOaPkOS6pOWIhuacn+eUs+ivtyAqLw0KICAgIHN1Ym1pdEluc3RhbGxtZW50QXBwbGljYXRpb24oKSB7DQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4gew0KICAgICAgICAvLyDkvb/nlKhFbGVtZW50IFVJ6KGo5Y2V6aqM6K+BDQogICAgICAgIHRoaXMuJHJlZnMuaW5zdGFsbG1lbnRGb3JtUmVmLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICAgIGlmICghdmFsaWQpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+WujOWWhOWIhuacn+eUs+ivt+S/oeaBrycpDQogICAgICAgICAgICByZWplY3QoKQ0KICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g6aKd5aSW55qE5Lia5Yqh6aqM6K+BDQogICAgICAgICAgaWYgKCF0aGlzLmluc3RhbGxtZW50Rm9ybS5sb2FuSWQpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+i0t+asvklE5LiN6IO95Li656m677yM6K+36YeN5paw5omT5byA6KGo5Y2VJykNCiAgICAgICAgICAgIHJlamVjdCgpDQogICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDpqozor4Hmr4/mnJ/otKbljZXph5Hpop3mmK/lkKblkIjnkIYNCiAgICAgICAgICBjb25zdCBiaWxsQW1vdW50ID0gTnVtYmVyKHRoaXMuaW5zdGFsbG1lbnRGb3JtLmJpbGxBbW91bnQpDQogICAgICAgICAgaWYgKGJpbGxBbW91bnQgPD0gMCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5q+P5pyf6LSm5Y2V6YeR6aKd5b+F6aG75aSn5LqOMO+8jOivt+ajgOafpeeUs+ivt+mHkemineWSjOacn+aVsCcpDQogICAgICAgICAgICByZWplY3QoKQ0KICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g6aqM6K+B5bC+5qy+6YC76L6RDQogICAgICAgICAgaWYgKHRoaXMuaW5zdGFsbG1lbnRGb3JtLnRhaWxBbW91bnQgPiAwICYmICF0aGlzLmluc3RhbGxtZW50Rm9ybS50YWlsUGF5VGltZSkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K6+572u5LqG5bC+5qy+6YeR6aKd5pe277yM5b+F6aG76YCJ5oup5bC+5qy+5pSv5LuY5pe26Ze0JykNCiAgICAgICAgICAgIHJlamVjdCgpDQogICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICB9DQoNCiAgICAgICAgICBjb25zb2xlLmxvZygn5o+Q5Lqk5YiG5pyf55Sz6K+35pWw5o2u77yaJywgdGhpcy5pbnN0YWxsbWVudEZvcm0pDQoNCiAgICAgICAgICAvLyDosIPnlKjliIbmnJ/nlLPor7dBUEnvvIjkuI7ku6Plgb/liIbmnJ/kvb/nlKjnm7jlkIznmoRBUEnvvIkNCiAgICAgICAgICBhZGRJbnN0YWxsbWVudF9hcHBsaWNhdGlvbl9hdWRpdCh0aGlzLmluc3RhbGxtZW50Rm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoJ+WIhuacn+eUs+ivt+aPkOS6pOaIkOWKnycpDQogICAgICAgICAgICAgIHJlc29sdmUoKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+WIhuacn+eUs+ivt+aPkOS6pOWksei0pe+8micgKyAocmVzcG9uc2UubXNnIHx8ICfmnKrnn6XplJnor68nKSkNCiAgICAgICAgICAgICAgcmVqZWN0KCkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCfliIbmnJ/nlLPor7fmj5DkuqTlpLHotKU6JywgZXJyb3IpDQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5YiG5pyf55Sz6K+35o+Q5Lqk5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VJykNCiAgICAgICAgICAgIHJlamVjdCgpDQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8qKiDmj5DkuqTms5Xor4nml6Xlv5fmlbDmja4gKi8NCiAgICBzdWJtaXRMaXRpZ2F0aW9uTG9nRGF0YShsb2FuUmVtaW5kZXJDb3B5LCBsaXRpZ2F0aW9uTG9nQ29weSkgew0KICAgICAgY29uc29sZS5sb2coJ+aPkOS6pOihqOWNleaVsOaNru+8micsIGxvYW5SZW1pbmRlckNvcHkpDQogICAgICBjb25zb2xlLmxvZygn5o+Q5Lqk6KGo5Y2V5pWw5o2u77yaJywgbGl0aWdhdGlvbkxvZ0NvcHkpDQogICAgICBzdWJtaXRMaXRpZ2F0aW9uTG9nKHsgbG9hblJlbWluZGVyOiBsb2FuUmVtaW5kZXJDb3B5LCBsaXRpZ2F0aW9uTG9nOiBsaXRpZ2F0aW9uTG9nQ29weSB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoJ+aPkOS6pOaIkOWKnycpDQogICAgICAgIHRoaXMudmlzaWJsZSA9IGZhbHNlDQogICAgICAgIHRoaXMucmVzZXRGb3JtKCkNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5Y+W5raI5pON5L2cICovDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy52aXNpYmxlID0gZmFsc2UNCiAgICAgIHRoaXMucmVzZXRGb3JtKCkNCiAgICAgIHJldHVybg0KICAgIH0sDQogICAgLyoqIOmHjee9ruihqOWNlSAqLw0KICAgIHJlc2V0Rm9ybSgpIHsNCiAgICAgIHRoaXMubG9hblJlbWluZGVyID0gew0KICAgICAgICBmdW5kc0ltYWdlOiBbXSwNCiAgICAgIH0NCiAgICAgIHRoaXMubGl0aWdhdGlvbkxvZyA9IHsNCiAgICAgICAgZG9jVXBsb2FkVXJsOiBbXSwNCiAgICAgIH0NCiAgICAgIHRoaXMuaW5zdGFsbG1lbnRGb3JtID0gew0KICAgICAgICBsb2FuSWQ6IG51bGwsDQogICAgICAgIGFwcGx5QW1vdW50OiAwLA0KICAgICAgICBwZXJpb2RDb3VudDogMSwNCiAgICAgICAgYmlsbEFtb3VudDogJzAuMDAnLA0KICAgICAgICB0YWlsQW1vdW50OiAwLA0KICAgICAgICByZXBheURheTogMSwNCiAgICAgICAgdGFpbFBheVRpbWU6IG51bGwsDQogICAgICAgIGFjY291bnRUeXBlOiAnJywNCiAgICAgICAgaW5zdGFsbG1lbnRTdGF0dXM6IDIgLy8gMi3ms5Xor4nliIbmnJ8NCiAgICAgIH0NCiAgICAgIC8vIOmHjee9ruWIhuacn+ihqOWNlemqjOivgeeKtuaAgQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICBpZiAodGhpcy4kcmVmcy5pbnN0YWxsbWVudEZvcm1SZWYpIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLmluc3RhbGxtZW50Rm9ybVJlZi5jbGVhclZhbGlkYXRlKCkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDnu5/kuIDmiZPlvIDlvLnnqpfnmoTmlrnms5UgKi8NCiAgICBvcGVuRGlhbG9nKCkgew0KICAgICAgdGhpcy52aXNpYmxlID0gdHJ1ZQ0KICAgICAgcmV0dXJuDQogICAgfSwNCiAgICAvKiog5aSE55CG5paH5Lu26LaF5Ye66ZmQ5Yi2ICovDQogICAgaGFuZGxlRXhjZWVkKGZpbGVzLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCflj6rog73kuIrkvKDkuIDkuKrmlofku7YnKQ0KICAgICAgcmV0dXJuDQogICAgfSwNCiAgfSwNCn0NCg=="}, {"version": 3, "sources": ["litigationLogForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2TA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "litigationLogForm.vue", "sourceRoot": "src/views/litigation/litigation/modules", "sourcesContent": ["<template>\r\n  <el-dialog :title=\"title\" :visible.sync=\"visible\" width=\"800px\" append-to-body @close=\"resetForm\">\r\n    <el-form ref=\"form\" :model=\"loanReminder\" label-width=\"120px\">\r\n      <!-- 第一部分：文书相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-document\"></i>\r\n        贷款信息\r\n      </el-divider>\r\n      <!-- 非填入字段 -->\r\n      <el-descriptions title=\"\" :column=\"3\" border>\r\n        <el-descriptions-item label=\"贷款人\">\r\n          {{ loanReminder.customerName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"出单渠道\">\r\n          {{ loanReminder.channel }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"放款银行\">\r\n          {{ loanReminder.bank }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <!-- 第一部分：文书相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-document\"></i>\r\n        文书信息\r\n      </el-divider>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"变更法诉状态\">\r\n            <litigation-status v-model=\"litigationLog.status\" placeholder=\"请选择法诉状态\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书名称\">\r\n            <el-select v-model=\"litigationLog.docName\" placeholder=\"请选择文书名称\" style=\"width: 100%\">\r\n              <el-option label=\"诉前调号\" value=\"诉前调号\" />\r\n              <el-option label=\"民初号\" value=\"民初号\" />\r\n              <el-option label=\"执行号\" value=\"执行号\" />\r\n              <el-option label=\"执保号\" value=\"执保号\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书号\">\r\n            <el-input v-model=\"litigationLog.docNumber\" placeholder=\"请输入文书号\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书生效\">\r\n            <el-date-picker v-model=\"litigationLog.docEffectiveDate\" type=\"date\" placeholder=\"选择日期\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\" v-if=\"litigationLog.status === '待出法院文书'\">\r\n          <el-form-item label=\"登记开庭时间\">\r\n            <el-date-picker v-model=\"litigationLog.openDate\" type=\"datetime\" placeholder=\"选择开庭时间\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"上传文书\">\r\n            <el-upload\r\n              :data=\"data\"\r\n              :action=\"uploadUrl\"\r\n              :headers=\"headers\"\r\n              :limit=\"1\"\r\n              :file-list=\"litigationLog.docUploadUrl\"\r\n              :on-success=\"(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'litigationLog.docUploadUrl')\"\r\n              :on-remove=\"(file, fileList) => handleRemove(file, fileList, 'litigationLog.docUploadUrl')\"\r\n              :on-error=\"handleUploadError\">\r\n              <el-button size=\"small\" type=\"primary\" :disabled=\"litigationLog.docUploadUrl.length >= 1\">点击上传</el-button>\r\n            </el-upload>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第二部分：还款相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-money\"></i>\r\n        还款信息\r\n      </el-divider>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"还款类型\">\r\n            <el-select v-model=\"loanReminder.repaymentStatus\" placeholder=\"请选择还款类型\" style=\"width: 100%\">\r\n              <el-option label=\"部分还款\" value=\"2\" />\r\n              <el-option label=\"分期还款\" value=\"3\" />\r\n              <el-option label=\"协商买车\" value=\"4\" />\r\n              <el-option label=\"法诉结清\" value=\"5\" />\r\n              <el-option label=\"法诉减免结清\" value=\"6\" />\r\n              <el-option label=\"拍卖回款\" value=\"7\" />\r\n              <el-option label=\"法院划扣\" value=\"8\" />\r\n              <el-option label=\"其他分配回款\" value=\"9\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"款项明细类型\">\r\n            <el-select v-model=\"loanReminder.fundsRepayment\" placeholder=\"请选择款项明细类型\" style=\"width: 100%\">\r\n              <el-option label=\"律师费\" value=\"律师费\" />\r\n              <el-option label=\"法诉费\" value=\"法诉费\" />\r\n              <el-option label=\"保全费\" value=\"保全费\" />\r\n              <el-option label=\"布控费\" value=\"布控费\" />\r\n              <el-option label=\"公告费\" value=\"公告费\" />\r\n              <el-option label=\"评估费\" value=\"评估费\" />\r\n              <el-option label=\"执行费\" value=\"执行费\" />\r\n              <el-option label=\"违约金\" value=\"违约金\" />\r\n              <el-option label=\"担保费\" value=\"担保费\" />\r\n              <el-option label=\"居间费\" value=\"居间费\" />\r\n              <el-option label=\"代偿金\" value=\"代偿金\" />\r\n              <el-option label=\"判决金额\" value=\"判决金额\" />\r\n              <el-option label=\"利息\" value=\"利息\" />\r\n              <el-option label=\"其他欠款\" value=\"其他欠款\" />\r\n              <el-option label=\"保险费\" value=\"保险费\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"金额\">\r\n            <el-input-number v-model=\"loanReminder.fundsAmount\" :min=\"0\" :precision=\"2\" style=\"width: 100%\" placeholder=\"请输入金额\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"账号类型\">\r\n            <el-select v-model=\"loanReminder.fundsAccountType\" placeholder=\"请选择账号类型\" style=\"width: 100%\">\r\n              <el-option label=\"银行账户\" value=\"银行账户\" />\r\n              <el-option label=\"微信\" value=\"微信\" />\r\n              <el-option label=\"支付宝\" value=\"支付宝\" />\r\n              <el-option label=\"其他\" value=\"其他\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"账号\">\r\n            <el-input v-model=\"loanReminder.accountNumber\" :disabled=\"loanReminder.fundsAccountType !== '其他'\" placeholder=\"请输入账号\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"还款凭据\">\r\n            <el-upload\r\n              :data=\"data\"\r\n              :action=\"uploadUrl\"\r\n              :headers=\"headers\"\r\n              list-type=\"picture-card\"\r\n              :file-list=\"loanReminder.fundsImage\"\r\n              :on-preview=\"handlePictureCardPreview\"\r\n              :on-success=\"(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'loanReminder.fundsImage')\"\r\n              :on-remove=\"(file, fileList) => handleRemove(file, fileList, 'loanReminder.fundsImage')\"\r\n              :on-error=\"handleUploadError\">\r\n              <i class=\"el-icon-plus\"></i>\r\n            </el-upload>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 分期表单 - 当选择分期还款时显示 -->\r\n      <div v-if=\"loanReminder.repaymentStatus === '3'\">\r\n        <el-divider content-position=\"left\">\r\n          <i class=\"el-icon-s-order\"></i>\r\n          分期申请信息\r\n        </el-divider>\r\n\r\n        <el-form ref=\"installmentFormRef\" :model=\"installmentForm\" :rules=\"installmentRules\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"申请分期金额\" prop=\"applyAmount\" required>\r\n                <el-input-number\r\n                  v-model=\"installmentForm.applyAmount\"\r\n                  :min=\"0.01\"\r\n                  :max=\"999999999\"\r\n                  :precision=\"2\"\r\n                  :step=\"100\"\r\n                  :controls-position=\"'right'\"\r\n                  placeholder=\"请输入申请分期金额\"\r\n                  @input=\"handleInstallmentFormChange\"\r\n                  style=\"width: 100%\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"分期期数\" prop=\"periodCount\" required>\r\n                <el-input-number\r\n                  v-model=\"installmentForm.periodCount\"\r\n                  :min=\"1\"\r\n                  :max=\"60\"\r\n                  :precision=\"0\"\r\n                  :step=\"1\"\r\n                  :controls-position=\"'right'\"\r\n                  placeholder=\"请输入分期期数\"\r\n                  @input=\"handleInstallmentFormChange\"\r\n                  style=\"width: 100%\" />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"每期账单金额\">\r\n                <el-input v-model=\"installmentForm.billAmount\" placeholder=\"自动计算\" disabled />\r\n                <div class=\"form-tip\">\r\n                  根据申请金额和期数自动计算\r\n                  <span v-if=\"installmentForm.applyAmount > 0 && installmentForm.periodCount > 0\">\r\n                    <br>计算公式：({{ installmentForm.applyAmount }} - {{ installmentForm.tailAmount || 0 }}) ÷ {{ installmentForm.periodCount }} = {{ installmentForm.billAmount }}元/期\r\n                  </span>\r\n                </div>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"每期还款日\" prop=\"repayDay\" required>\r\n                <el-select v-model=\"installmentForm.repayDay\" placeholder=\"请选择每期还款日\" style=\"width: 100%\">\r\n                  <el-option v-for=\"day in 28\" :key=\"day\" :label=\"`每月${day}号`\" :value=\"day\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"尾款金额\">\r\n                <el-input-number\r\n                  v-model=\"installmentForm.tailAmount\"\r\n                  :min=\"0\"\r\n                  :precision=\"2\"\r\n                  :step=\"100\"\r\n                  :controls-position=\"'right'\"\r\n                  placeholder=\"请输入尾款金额（可选）\"\r\n                  @input=\"handleInstallmentFormChange\"\r\n                  style=\"width: 100%\" />\r\n                <div class=\"form-tip\">可选，如有尾款请填写</div>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"尾款支付时间\" :prop=\"installmentForm.tailAmount > 0 ? 'tailPayTime' : ''\">\r\n                <el-date-picker\r\n                  v-model=\"installmentForm.tailPayTime\"\r\n                  type=\"date\"\r\n                  placeholder=\"选择尾款支付时间\"\r\n                  :disabled=\"!installmentForm.tailAmount || installmentForm.tailAmount <= 0\"\r\n                  style=\"width: 100%\" />\r\n                <div class=\"form-tip\">有尾款时必填</div>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"账号类型\" prop=\"accountType\" required>\r\n                <el-select v-model=\"installmentForm.accountType\" placeholder=\"请选择账号类型\" style=\"width: 100%\">\r\n                  <el-option label=\"银行账户\" value=\"银行账户\" />\r\n                  <el-option label=\"微信\" value=\"微信\" />\r\n                  <el-option label=\"支付宝\" value=\"支付宝\" />\r\n                  <el-option label=\"其他\" value=\"其他\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 第三部分：日志相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-notebook-2\"></i>\r\n        日志信息\r\n      </el-divider>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"日志类型\">\r\n            <el-select v-model=\"loanReminder.urgeStatus\" placeholder=\"请选择日志类型\" style=\"width: 100%\">\r\n              <el-option label=\"继续跟踪\" value=\"1\" />\r\n              <el-option label=\"约定还款\" value=\"2\" />\r\n              <el-option label=\"无法跟进\" value=\"3\" />\r\n              <el-option label=\"暂时无需跟进\" value=\"4\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"下次跟进时间\">\r\n            <el-date-picker v-model=\"loanReminder.trackingTime\" type=\"datetime\" placeholder=\"选择跟进时间\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"日志描述\">\r\n            <el-input v-model=\"loanReminder.urgeDescribe\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入日志描述\" maxlength=\"500\" show-word-limit />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"cancel\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n    </div>\r\n    <!-- 图片预览 -->\r\n    <el-dialog :visible.sync=\"dialogVisible\">\r\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt=\"\" />\r\n    </el-dialog>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport litigationStatus from '@/layout/components/Dialog/litigationStatus.vue'\r\nimport { getToken } from '@/utils/auth'\r\nimport { submitLitigationLog } from '@/api/litigation/litigation'\r\nimport { addInstallment_application_audit } from '@/api/installment_application_audit/installment_application_audit'\r\n\r\nexport default {\r\n  name: 'LitigationLogForm',\r\n  components: {\r\n    litigationStatus,\r\n  },\r\n  props: {\r\n    action: {\r\n      type: String,\r\n      default: '/common/ossupload',\r\n    },\r\n    data: {\r\n      type: Object,\r\n      default: () => {},\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      title: '提交法诉日志',\r\n      visible: false,\r\n      loanReminder: {},\r\n      litigationLog: {},\r\n      installmentForm: {\r\n        loanId: null,\r\n        applyAmount: 0,\r\n        periodCount: 1,\r\n        billAmount: '0.00',\r\n        tailAmount: 0,\r\n        repayDay: 1,\r\n        tailPayTime: null,\r\n        accountType: '',\r\n        installmentStatus: 2 // 2-法诉分期\r\n      },\r\n      // 分期表单验证规则\r\n      installmentRules: {\r\n        applyAmount: [\r\n          { required: true, message: '请输入申请分期金额', trigger: 'blur' },\r\n          { type: 'number', min: 0.01, message: '申请分期金额必须大于0', trigger: 'blur' }\r\n        ],\r\n        periodCount: [\r\n          { required: true, message: '请输入分期期数', trigger: 'blur' },\r\n          { type: 'number', min: 1, max: 60, message: '分期期数必须在1-60期之间', trigger: 'blur' }\r\n        ],\r\n        repayDay: [\r\n          { required: true, message: '请选择每期还款日', trigger: 'change' }\r\n        ],\r\n        accountType: [\r\n          { required: true, message: '请选择账号类型', trigger: 'change' }\r\n        ],\r\n        tailPayTime: [\r\n          {\r\n            validator: (rule, value, callback) => {\r\n              if (this.installmentForm.tailAmount > 0 && !value) {\r\n                callback(new Error('有尾款时必须选择尾款支付时间'))\r\n              } else {\r\n                callback()\r\n              }\r\n            },\r\n            trigger: 'change'\r\n          }\r\n        ]\r\n      },\r\n      uploadUrl: process.env.VUE_APP_BASE_API + this.action,\r\n      headers: {\r\n        Authorization: 'Bearer ' + getToken(),\r\n      },\r\n      dialogImageUrl: '',\r\n      dialogVisible: false,\r\n    }\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          console.log('newVal', newVal)\r\n          this.loanReminder = {\r\n            loanId: newVal.流程序号,\r\n            customerName: newVal.贷款人,\r\n            channel: newVal.出单渠道,\r\n            bank: newVal.放款银行,\r\n            identity: this.$store.state.user.roles[0],\r\n            repaymentStatus: '',\r\n            fundsRepayment: '',\r\n            fundsAmount: '',\r\n            fundsImage: [],\r\n            fundsAccountType: '',\r\n            accountNumber: '',\r\n            urgeStatus: '',\r\n            trackingTime: '',\r\n            urgeDescribe: '',\r\n            status: 2,\r\n          }\r\n          this.litigationLog = {\r\n            loanId: newVal.流程序号,\r\n            litigationId: newVal.序号,\r\n            docName: '',\r\n            docNumber: '',\r\n            docUploadUrl: [],\r\n            docEffectiveDate: '',\r\n            openDate: '',\r\n            status: '',\r\n          }\r\n          // 重置分期表单\r\n          this.installmentForm = {\r\n            loanId: newVal && newVal.流程序号 ? newVal.流程序号 : null,\r\n            applyAmount: 0,\r\n            periodCount: 1,\r\n            billAmount: '0.00',\r\n            tailAmount: 0,\r\n            repayDay: 1,\r\n            tailPayTime: null,\r\n            accountType: '',\r\n            installmentStatus: 2 // 2-法诉分期\r\n          }\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true,\r\n    },\r\n  },\r\n  methods: {\r\n    // 分期表单计算方法\r\n    handleInstallmentFormChange() {\r\n      const applyAmount = Number(this.installmentForm.applyAmount) || 0\r\n      const periodCount = Number(this.installmentForm.periodCount) || 1\r\n      const tailAmount = Number(this.installmentForm.tailAmount) || 0\r\n      if (applyAmount >= 0 && periodCount >= 1) {\r\n        this.installmentForm.billAmount = ((applyAmount - tailAmount) / periodCount).toFixed(2)\r\n      } else {\r\n        this.installmentForm.billAmount = '0.00'\r\n      }\r\n    },\r\n    // 通用的上传成功处理函数\r\n    handleUploadSuccess(res, file, fileList, formField) {\r\n      const [obj, prop] = formField.split('.')\r\n      this[obj][prop] = fileList\r\n    },\r\n    // 通用的删除处理函数\r\n    handleRemove(file, fileList, formField) {\r\n      const [obj, prop] = formField.split('.')\r\n      this[obj][prop] = fileList\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError('上传失败，请重试')\r\n      this.$modal.closeLoading()\r\n    },\r\n    // 图片预览\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url\r\n      this.dialogVisible = true\r\n    },\r\n    /** 提交表单 */\r\n    submitForm() {\r\n      const loanReminderCopy = JSON.parse(JSON.stringify(this.loanReminder))\r\n      const litigationLogCopy = JSON.parse(JSON.stringify(this.litigationLog))\r\n      loanReminderCopy.fundsImage = loanReminderCopy.fundsImage.map(item => item.response).join(',')\r\n      litigationLogCopy.docUploadUrl = litigationLogCopy.docUploadUrl.map(item => item.response).join(',')\r\n      loanReminderCopy.fundsAccountType =\r\n        loanReminderCopy.fundsAccountType === '其他' ? loanReminderCopy.accountNumber : loanReminderCopy.fundsAccountType\r\n      // 将日志描述从 loanReminder 复制到 litigationLog\r\n      litigationLogCopy.urgeDescribe = loanReminderCopy.urgeDescribe\r\n\r\n      // 如果选择了分期还款，先提交分期申请\r\n      if (loanReminderCopy.repaymentStatus === '3') {\r\n        // 显示确认对话框\r\n        const confirmMessage = `确认提交分期申请？\\n申请金额：${this.installmentForm.applyAmount}元\\n分期期数：${this.installmentForm.periodCount}期\\n每期金额：${this.installmentForm.billAmount}元`\r\n        this.$confirm(confirmMessage, '确认分期申请', {\r\n          confirmButtonText: '确定提交',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.submitInstallmentApplication().then(() => {\r\n            // 分期申请提交成功后，再提交日志\r\n            this.submitLitigationLogData(loanReminderCopy, litigationLogCopy)\r\n          }).catch(() => {\r\n            this.$modal.msgError('分期申请提交失败')\r\n          })\r\n        }).catch(() => {\r\n          // 用户取消了操作\r\n        })\r\n      } else {\r\n        // 直接提交日志\r\n        this.submitLitigationLogData(loanReminderCopy, litigationLogCopy)\r\n      }\r\n    },\r\n\r\n    /** 提交分期申请 */\r\n    submitInstallmentApplication() {\r\n      return new Promise((resolve, reject) => {\r\n        // 使用Element UI表单验证\r\n        this.$refs.installmentFormRef.validate((valid) => {\r\n          if (!valid) {\r\n            this.$message.error('请完善分期申请信息')\r\n            reject()\r\n            return\r\n          }\r\n\r\n          // 额外的业务验证\r\n          if (!this.installmentForm.loanId) {\r\n            this.$message.error('贷款ID不能为空，请重新打开表单')\r\n            reject()\r\n            return\r\n          }\r\n\r\n          // 验证每期账单金额是否合理\r\n          const billAmount = Number(this.installmentForm.billAmount)\r\n          if (billAmount <= 0) {\r\n            this.$message.error('每期账单金额必须大于0，请检查申请金额和期数')\r\n            reject()\r\n            return\r\n          }\r\n\r\n          // 验证尾款逻辑\r\n          if (this.installmentForm.tailAmount > 0 && !this.installmentForm.tailPayTime) {\r\n            this.$message.error('设置了尾款金额时，必须选择尾款支付时间')\r\n            reject()\r\n            return\r\n          }\r\n\r\n          console.log('提交分期申请数据：', this.installmentForm)\r\n\r\n          // 调用分期申请API（与代偿分期使用相同的API）\r\n          addInstallment_application_audit(this.installmentForm).then(response => {\r\n            if (response.code === 200) {\r\n              this.$modal.msgSuccess('分期申请提交成功')\r\n              resolve()\r\n            } else {\r\n              this.$modal.msgError('分期申请提交失败：' + (response.msg || '未知错误'))\r\n              reject()\r\n            }\r\n          }).catch(error => {\r\n            console.error('分期申请提交失败:', error)\r\n            this.$modal.msgError('分期申请提交失败，请稍后重试')\r\n            reject()\r\n          })\r\n        })\r\n      })\r\n    },\r\n\r\n    /** 提交法诉日志数据 */\r\n    submitLitigationLogData(loanReminderCopy, litigationLogCopy) {\r\n      console.log('提交表单数据：', loanReminderCopy)\r\n      console.log('提交表单数据：', litigationLogCopy)\r\n      submitLitigationLog({ loanReminder: loanReminderCopy, litigationLog: litigationLogCopy }).then(res => {\r\n        this.$modal.msgSuccess('提交成功')\r\n        this.visible = false\r\n        this.resetForm()\r\n      })\r\n    },\r\n    /** 取消操作 */\r\n    cancel() {\r\n      this.visible = false\r\n      this.resetForm()\r\n      return\r\n    },\r\n    /** 重置表单 */\r\n    resetForm() {\r\n      this.loanReminder = {\r\n        fundsImage: [],\r\n      }\r\n      this.litigationLog = {\r\n        docUploadUrl: [],\r\n      }\r\n      this.installmentForm = {\r\n        loanId: null,\r\n        applyAmount: 0,\r\n        periodCount: 1,\r\n        billAmount: '0.00',\r\n        tailAmount: 0,\r\n        repayDay: 1,\r\n        tailPayTime: null,\r\n        accountType: '',\r\n        installmentStatus: 2 // 2-法诉分期\r\n      }\r\n      // 重置分期表单验证状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.installmentFormRef) {\r\n          this.$refs.installmentFormRef.clearValidate()\r\n        }\r\n      })\r\n    },\r\n    /** 统一打开弹窗的方法 */\r\n    openDialog() {\r\n      this.visible = true\r\n      return\r\n    },\r\n    /** 处理文件超出限制 */\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning('只能上传一个文件')\r\n      return\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.el-divider__text {\r\n  padding: 0 15px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.el-upload {\r\n  width: 100%;\r\n}\r\n\r\n/* 分期表单样式 */\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 4px;\r\n  line-height: 1.2;\r\n}\r\n\r\n/* 必填项标识 */\r\n.el-form-item.is-required .el-form-item__label::before {\r\n  content: '*';\r\n  color: #f56c6c;\r\n  margin-right: 4px;\r\n}\r\n\r\n/* 分期表单区域样式 */\r\n.el-form .el-row {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n/* 禁用状态的输入框样式 */\r\n.el-input.is-disabled .el-input__inner {\r\n  background-color: #f5f7fa;\r\n  border-color: #e4e7ed;\r\n  color: #c0c4cc;\r\n}\r\n\r\n/* 分期表单边框 */\r\n.el-form {\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  padding: 20px;\r\n  background-color: #fafafa;\r\n}\r\n</style>\r\n"]}]}